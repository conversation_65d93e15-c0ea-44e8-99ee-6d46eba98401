{% extends "base.html" %}

{% block title %}لوحة التحكم - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-house"></i>
        مرحباً، {{ current_user.full_name }}
    </h2>
    <a href="/requests/new" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i>
        طلب جديد
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card card-stats bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ stats.total }}</h3>
                        <p class="card-text">إجمالي الطلبات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-stats bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ stats.pending }}</h3>
                        <p class="card-text">طلبات قيد المراجعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card card-stats bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ stats.completed }}</h3>
                        <p class="card-text">طلبات مكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Requests -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-text"></i>
                    الطلبات الأخيرة
                </h5>
                <a href="/requests" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if requests %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العنوان</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for req in requests %}
                            <tr>
                                <td>
                                    <code>{{ req.request_number }}</code>
                                </td>
                                <td>{{ req.request_title }}</td>
                                <td>
                                    {% if req.status.value == 'pending' %}
                                    <span class="badge bg-warning status-badge">قيد المراجعة</span>
                                    {% elif req.status.value == 'in_progress' %}
                                    <span class="badge bg-info status-badge">قيد التنفيذ</span>
                                    {% elif req.status.value == 'completed' %}
                                    <span class="badge bg-success status-badge">مكتمل</span>
                                    {% elif req.status.value == 'rejected' %}
                                    <span class="badge bg-danger status-badge">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>{{ req.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-file-earmark-text display-4 text-muted"></i>
                    <p class="text-muted mt-2">لا توجد طلبات حتى الآن</p>
                    <a href="/requests/new" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء طلب جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i>
                    النشاطات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if activities %}
                {% for activity in activities %}
                <div class="activity-item">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            {% if activity.activity_type.value == 'login' %}
                            <i class="bi bi-box-arrow-in-right text-success"></i>
                            {% elif activity.activity_type.value == 'request_created' %}
                            <i class="bi bi-plus-circle text-primary"></i>
                            {% elif activity.activity_type.value == 'file_uploaded' %}
                            <i class="bi bi-upload text-info"></i>
                            {% else %}
                            <i class="bi bi-activity text-secondary"></i>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 small">{{ activity.description }}</p>
                            <small class="text-muted">{{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-activity display-6 text-muted"></i>
                    <p class="text-muted mt-2 small">لا توجد نشاطات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
