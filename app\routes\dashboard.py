from fastapi import APIRouter, Depends, Request, Form, UploadFile, File as FastAPIFile, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.utils.auth import verify_token
from app.services.user_service import UserService
from app.services.request_service import RequestService
from app.models.user import User, UserRole
from app.models.request import RequestStatus
from app.models.activity import ActivityType

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


async def get_current_user_cookie(request: Request, db: Session = Depends(get_db)) -> User:
    """Get current user using cookie authentication"""
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=403, detail="Not authenticated")

    # Remove 'Bearer ' prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=403, detail="Invalid token")

    username = payload.get("sub")
    if not username:
        raise HTTPException(status_code=403, detail="Invalid token")

    user = UserService.get_user_by_username(db, username)
    if not user:
        raise HTTPException(status_code=403, detail="User not found")

    if not user.is_active:
        raise HTTPException(status_code=403, detail="Inactive user")

    return user


@router.get("/dashboard", response_class=HTMLResponse)
async def user_dashboard(
    request: Request,
    current_user: User = Depends(get_current_user_cookie),
    db: Session = Depends(get_db)
):
    """User dashboard"""
    # Get user's requests
    user_requests = RequestService.get_user_requests(db, current_user.id, limit=10)
    
    # Get recent activities
    activities = UserService.get_user_activities(db, current_user.id, limit=10)
    
    # Get request statistics for user
    total_requests = len(RequestService.get_user_requests(db, current_user.id, limit=1000))
    pending_requests = len([r for r in user_requests if r.status == RequestStatus.PENDING])
    completed_requests = len([r for r in user_requests if r.status == RequestStatus.COMPLETED])
    
    return templates.TemplateResponse(
        "dashboard/user_dashboard.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": user_requests,
            "activities": activities,
            "stats": {
                "total": total_requests,
                "pending": pending_requests,
                "completed": completed_requests
            }
        }
    )


@router.get("/requests/new", response_class=HTMLResponse)
async def new_request_form(
    request: Request,
    current_user: User = Depends(get_current_user_cookie)
):
    """Display new request form"""
    return templates.TemplateResponse(
        "requests/new_request.html",
        {
            "request": request,
            "current_user": current_user
        }
    )


@router.post("/requests/new")
async def create_new_request(
    request: Request,
    request_name: str = Form(...),
    request_title: str = Form(...),
    description: Optional[str] = Form(None),
    files: List[UploadFile] = FastAPIFile(None),
    current_user: User = Depends(get_current_user_cookie),
    db: Session = Depends(get_db)
):
    """Create new request"""
    try:
        # Create request
        new_request = RequestService.create_request(
            db=db,
            user_id=current_user.id,
            request_name=request_name,
            request_title=request_title,
            description=description
        )
        
        # Add files if provided
        if files and files[0].filename:  # Check if files were actually uploaded
            await RequestService.add_files_to_request(db, new_request.id, files)
        
        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.REQUEST_CREATED,
            description=f"Created request: {request_title}",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        return templates.TemplateResponse(
            "requests/request_success.html",
            {
                "request": request,
                "current_user": current_user,
                "new_request": new_request,
                "success": "Request created successfully!"
            }
        )
        
    except Exception as e:
        return templates.TemplateResponse(
            "requests/new_request.html",
            {
                "request": request,
                "current_user": current_user,
                "error": str(e)
            },
            status_code=400
        )


@router.get("/requests/{request_id}", response_class=HTMLResponse)
async def view_request(
    request: Request,
    request_id: int,
    current_user: User = Depends(get_current_user_cookie),
    db: Session = Depends(get_db)
):
    """View request details"""
    req = RequestService.get_request_by_id(db, request_id)
    
    if not req:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )
    
    # Check if user can view this request
    if current_user.role != UserRole.ADMIN and req.user_id != current_user.id:
        return templates.TemplateResponse(
            "errors/403.html",
            {"request": request, "current_user": current_user},
            status_code=403
        )
    
    return templates.TemplateResponse(
        "requests/view_request.html",
        {
            "request": request,
            "current_user": current_user,
            "req": req
        }
    )


@router.get("/requests", response_class=HTMLResponse)
async def list_requests(
    request: Request,
    current_user: User = Depends(get_current_user_cookie),
    db: Session = Depends(get_db)
):
    """List user's requests"""
    requests = RequestService.get_user_requests(db, current_user.id, limit=50)
    
    return templates.TemplateResponse(
        "requests/list_requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": requests
        }
    )


@router.get("/profile", response_class=HTMLResponse)
async def user_profile(
    request: Request,
    current_user: User = Depends(get_current_user_cookie)
):
    """User profile page"""
    return templates.TemplateResponse(
        "profile/profile.html",
        {
            "request": request,
            "current_user": current_user
        }
    )


@router.post("/profile/update")
async def update_profile(
    request: Request,
    full_name: str = Form(...),
    email: str = Form(...),
    current_user: User = Depends(get_current_user_cookie),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    try:
        updated_user = UserService.update_user(
            db=db,
            user_id=current_user.id,
            full_name=full_name,
            email=email
        )
        
        # Log activity
        UserService.log_activity(
            db=db,
            user_id=current_user.id,
            activity_type=ActivityType.PROFILE_UPDATED,
            description="Profile updated",
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        return templates.TemplateResponse(
            "profile/profile.html",
            {
                "request": request,
                "current_user": updated_user,
                "success": "Profile updated successfully!"
            }
        )
        
    except Exception as e:
        return templates.TemplateResponse(
            "profile/profile.html",
            {
                "request": request,
                "current_user": current_user,
                "error": str(e)
            },
            status_code=400
        )
