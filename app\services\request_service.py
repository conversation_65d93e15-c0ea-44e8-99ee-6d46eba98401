from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from app.models.request import Request, RequestStatus
from app.models.file import File
from app.models.user import User
from app.utils.file_handler import <PERSON>Handler
from fastapi import UploadFile, HTTPException


class RequestService:
    """Service for request operations"""
    
    @staticmethod
    def create_request(
        db: Session,
        user_id: int,
        request_name: str,
        request_title: str,
        description: Optional[str] = None
    ) -> Request:
        """Create a new request"""
        request = Request(
            request_name=request_name,
            request_title=request_title,
            request_number=Request.generate_request_number(),
            unique_code=Request.generate_unique_code(),
            description=description,
            user_id=user_id
        )
        
        db.add(request)
        db.commit()
        db.refresh(request)
        
        return request
    
    @staticmethod
    def get_request_by_id(db: Session, request_id: int) -> Optional[Request]:
        """Get request by ID"""
        return db.query(Request).filter(Request.id == request_id).first()
    
    @staticmethod
    def get_request_by_number(db: Session, request_number: str) -> Optional[Request]:
        """Get request by request number"""
        return db.query(Request).filter(Request.request_number == request_number).first()
    
    @staticmethod
    def get_user_requests(
        db: Session, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 50
    ) -> List[Request]:
        """Get requests for a specific user"""
        return db.query(Request).filter(
            Request.user_id == user_id
        ).order_by(desc(Request.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_all_requests(
        db: Session, 
        skip: int = 0, 
        limit: int = 50,
        status: Optional[RequestStatus] = None
    ) -> List[Request]:
        """Get all requests with optional status filter"""
        query = db.query(Request)
        
        if status:
            query = query.filter(Request.status == status)
        
        return query.order_by(desc(Request.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def update_request_status(
        db: Session, 
        request_id: int, 
        status: RequestStatus
    ) -> Optional[Request]:
        """Update request status"""
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            return None
        
        request.status = status
        db.commit()
        db.refresh(request)
        
        return request
    
    @staticmethod
    def update_request(
        db: Session,
        request_id: int,
        request_name: Optional[str] = None,
        request_title: Optional[str] = None,
        description: Optional[str] = None,
        status: Optional[RequestStatus] = None
    ) -> Optional[Request]:
        """Update request information"""
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            return None
        
        if request_name is not None:
            request.request_name = request_name
        
        if request_title is not None:
            request.request_title = request_title
        
        if description is not None:
            request.description = description
        
        if status is not None:
            request.status = status
        
        db.commit()
        db.refresh(request)
        
        return request
    
    @staticmethod
    async def add_files_to_request(
        db: Session,
        request_id: int,
        files: List[UploadFile]
    ) -> List[File]:
        """Add files to a request"""
        # Validate file count
        FileHandler.validate_file_count(files)
        
        # Check if request exists
        request = db.query(Request).filter(Request.id == request_id).first()
        if not request:
            raise HTTPException(status_code=404, detail="Request not found")
        
        # Check current file count
        current_file_count = db.query(File).filter(File.request_id == request_id).count()
        if current_file_count + len(files) > 5:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot add {len(files)} files. Maximum 5 files per request. Current: {current_file_count}"
            )
        
        saved_files = []
        
        try:
            for file in files:
                # Save file to disk
                file_info = await FileHandler.save_file(file, request_id)
                
                # Create file record in database
                db_file = File(
                    original_filename=file_info["original_filename"],
                    stored_filename=file_info["stored_filename"],
                    file_path=file_info["file_path"],
                    file_size=file_info["file_size"],
                    file_type=file_info["file_type"],
                    mime_type=file_info["mime_type"],
                    request_id=request_id
                )
                
                db.add(db_file)
                saved_files.append(db_file)
            
            db.commit()
            
            # Refresh all files
            for file in saved_files:
                db.refresh(file)
            
            return saved_files
            
        except Exception as e:
            # Rollback database changes
            db.rollback()
            
            # Clean up any saved files
            for file in saved_files:
                if hasattr(file, 'file_path'):
                    FileHandler.delete_file(file.file_path)
            
            raise e
    
    @staticmethod
    def delete_file_from_request(db: Session, file_id: int) -> bool:
        """Delete a file from a request"""
        file = db.query(File).filter(File.id == file_id).first()
        if not file:
            return False
        
        # Delete file from disk
        FileHandler.delete_file(file.file_path)
        
        # Delete file record from database
        db.delete(file)
        db.commit()
        
        return True
    
    @staticmethod
    def get_request_statistics(db: Session) -> dict:
        """Get request statistics"""
        total_requests = db.query(Request).count()
        pending_requests = db.query(Request).filter(Request.status == RequestStatus.PENDING).count()
        in_progress_requests = db.query(Request).filter(Request.status == RequestStatus.IN_PROGRESS).count()
        completed_requests = db.query(Request).filter(Request.status == RequestStatus.COMPLETED).count()
        rejected_requests = db.query(Request).filter(Request.status == RequestStatus.REJECTED).count()
        
        return {
            "total": total_requests,
            "pending": pending_requests,
            "in_progress": in_progress_requests,
            "completed": completed_requests,
            "rejected": rejected_requests
        }
